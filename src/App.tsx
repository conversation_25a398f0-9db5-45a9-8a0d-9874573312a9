import React, { useState, useRef, useEffect } from 'react';
import { Send, Video, <PERSON>rk<PERSON>, Clock, CheckCircle, AlertCircle, Download } from 'lucide-react';
import { VideoPlayer } from './components/VideoPlayer';
import { GenerationProgressBar } from './components/GenerationProgressBar';
import { WEBHOOK_CONFIG } from './config/webhook';

interface VideoGeneration {
  id: string;
  prompt: string;
  status: 'generating' | 'completed' | 'failed';
  videoUrl?: string;
  timestamp: Date;
  duration?: number;
  // timerComplete?: boolean; // Removed
  webhookResponded?: boolean;
  webhookError?: boolean;
  errorMessage?: string; // Add error message field
  pollingAttempts?: number; // Track polling attempts
  lastPollingTime?: Date; // Track last polling time
}

function App() {
  const [prompt, setPrompt] = useState('');
  const [generations, setGenerations] = useState<VideoGeneration[]>([]);
  const [isGeneratingOverall, setIsGeneratingOverall] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Polling function to check for video completion
  const pollForVideoCompletion = async (generationId: string) => {
    const generation = generations.find(gen => gen.id === generationId);
    if (!generation) return;

    const pollingAttempts = (generation.pollingAttempts || 0) + 1;
    console.log(`[App.tsx] Polling attempt ${pollingAttempts}/${WEBHOOK_CONFIG.MAX_POLLING_ATTEMPTS} for ID ${generationId}`);

    // Update polling attempts
    setGenerations(prevGens =>
      prevGens.map(gen =>
        gen.id === generationId
          ? { ...gen, pollingAttempts, lastPollingTime: new Date() }
          : gen
      )
    );

    if (pollingAttempts > WEBHOOK_CONFIG.MAX_POLLING_ATTEMPTS) {
      console.error(`[App.tsx] Max polling attempts reached for ID ${generationId}`);
      setGenerations(prevGens =>
        prevGens.map(gen =>
          gen.id === generationId
            ? { ...gen, status: 'failed', errorMessage: 'Video generation timed out after maximum polling attempts' }
            : gen
        )
      );
      return;
    }

    try {
      const webhookUrl = WEBHOOK_CONFIG.getWebhookUrl();
      const headers = WEBHOOK_CONFIG.getHeaders();

      const response = await WEBHOOK_CONFIG.fetchWithTimeout(webhookUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          prompt: generation.prompt,
          id: generation.id,
          timestamp: new Date().toISOString(),
        }),
      }, 60000); // 1 minute timeout for polling requests

      if (response.ok) {
        const responseText = await response.text();
        const responseData = JSON.parse(responseText);

        if (Array.isArray(responseData) && responseData.length > 0) {
          const videoUrl = responseData[0]?.response?.body?.result;
          if (videoUrl && typeof videoUrl === 'string' && videoUrl.startsWith('http')) {
            // Video is ready!
            console.log(`[App.tsx] Video ready after ${pollingAttempts} polling attempts for ID ${generationId}:`, videoUrl);
            setGenerations(prevGens =>
              prevGens.map(gen =>
                gen.id === generationId
                  ? { ...gen, status: 'completed', videoUrl, webhookResponded: true, duration: 420 }
                  : gen
              )
            );
            return;
          }
        }
      }

      // Video not ready yet, schedule next poll
      setTimeout(() => pollForVideoCompletion(generationId), WEBHOOK_CONFIG.POLLING_INTERVAL);

    } catch (error) {
      console.error(`[App.tsx] Polling error for ID ${generationId}:`, error);
      // Continue polling unless it's a critical error
      setTimeout(() => pollForVideoCompletion(generationId), WEBHOOK_CONFIG.POLLING_INTERVAL);
    }
  };



  useEffect(() => {
    scrollToBottom();
  }, [generations]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || isGeneratingOverall) return;

    const newGeneration: VideoGeneration = {
      id: Date.now().toString(),
      prompt: prompt.trim(),
      status: 'generating',
      timestamp: new Date(),
      // timerComplete: false, // Removed
      webhookResponded: false,
      webhookError: false,
    };

    setGenerations(prev => [...prev, newGeneration]);
    setIsGeneratingOverall(true);
    setPrompt('');

    console.log(`[App.tsx] handleSubmit: Initiating fetch for generation ID ${newGeneration.id}`);
    const webhookUrl = WEBHOOK_CONFIG.getWebhookUrl();
    const headers = WEBHOOK_CONFIG.getHeaders();
    console.log(`[App.tsx] Using webhook URL: ${webhookUrl}`);

    // Use extended timeout for initial request
    WEBHOOK_CONFIG.fetchWithTimeout(webhookUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        prompt: newGeneration.prompt,
        id: newGeneration.id,
        timestamp: new Date().toISOString(),
      }),
    }, WEBHOOK_CONFIG.INITIAL_TIMEOUT)
    .then(async response => {
      console.log(`[App.tsx] Webhook response status for ID ${newGeneration.id}:`, response.status, response.statusText);
      console.log(`[App.tsx] Response headers:`, Object.fromEntries(response.headers.entries()));
      console.log(`[App.tsx] Response URL:`, response.url);
      console.log(`[App.tsx] Response type:`, response.type);

      if (response.ok) {
        let actualVideoUrl: string | undefined;
        let actualDuration: number | undefined; // Assuming duration is no longer sent or needed from this response

        try {
          // Parse the JSON response which has the format:
          // [{ "response": { "body": { "result": "video_url" }, "headers": {}, "statusCode": 200 } }]
          const responseText = await response.text();
          console.log(`[App.tsx] Webhook response TEXT for ID ${newGeneration.id}:`, responseText);

          const responseData = JSON.parse(responseText);
          console.log(`[App.tsx] Parsed webhook response for ID ${newGeneration.id}:`, responseData);

          // Extract video URL from the nested structure
          if (Array.isArray(responseData) && responseData.length > 0) {
            const firstResponse = responseData[0];
            if (firstResponse.response && firstResponse.response.body && firstResponse.response.body.result) {
              actualVideoUrl = firstResponse.response.body.result;
              console.log(`[App.tsx] Extracted video URL for ID ${newGeneration.id}:`, actualVideoUrl);

              // Validate if it's a valid URL
              if (typeof actualVideoUrl === 'string' && actualVideoUrl.trim() !== '' &&
                  (actualVideoUrl.startsWith('http://') || actualVideoUrl.startsWith('https://'))) {
                // URL is valid - video is ready!
                console.log(`[App.tsx] Video URL validated successfully for ID ${newGeneration.id}`);
              } else {
                console.error(`[App.tsx] Invalid video URL format for ID ${newGeneration.id}:`, actualVideoUrl);
                actualVideoUrl = undefined;
              }
            } else {
              // Response structure exists but no video URL yet - start polling
              console.log(`[App.tsx] Video not ready yet for ID ${newGeneration.id}, starting polling...`);
              actualVideoUrl = undefined;
              // Mark as still generating and start polling
              setGenerations(prevGens =>
                prevGens.map(gen =>
                  gen.id === newGeneration.id
                    ? { ...gen, webhookResponded: true, pollingAttempts: 0 }
                    : gen
                )
              );
              // Start polling after a short delay
              setTimeout(() => pollForVideoCompletion(newGeneration.id), WEBHOOK_CONFIG.POLLING_INTERVAL);
              return; // Exit early to avoid setting completed status
            }
          } else {
            console.error(`[App.tsx] Response is not an array or is empty for ID ${newGeneration.id}:`, responseData);
            actualVideoUrl = undefined;
          }

        } catch (parseError) {
          console.error(`[App.tsx] Failed to parse JSON response for ID ${newGeneration.id}:`, parseError);
          setGenerations(prevGens =>
            prevGens.map(gen =>
              gen.id === newGeneration.id
                ? { ...gen, webhookResponded: true, webhookError: true, videoUrl: undefined }
                : gen
            )
          );
          return;
        }
        
        setGenerations(prevGens =>
          prevGens.map(gen => {
            if (gen.id === newGeneration.id) {
              const updatedGen = {
                ...gen,
                videoUrl: actualVideoUrl,
                webhookResponded: true,
                duration: actualDuration,
                webhookError: !actualVideoUrl,
              };
              // if (updatedGen.timerComplete) { // Logic now depends only on webhookResponded
              if (actualVideoUrl) {
                updatedGen.status = 'completed';
                updatedGen.duration = updatedGen.duration ?? 420; // Default duration if not provided
              } else {
                updatedGen.status = 'failed';
              }
              // } // End of removed timerComplete block
              return updatedGen;
            }
            return gen;
          })
        );
      } else {
        const errorBody = await response.text().catch(() => "Could not read error body");
        console.error(`[App.tsx] Webhook HTTP error for ID ${newGeneration.id}! Status: ${response.status}. Body:`, errorBody);
        console.error(`[App.tsx] Full response object:`, response);

        // Parse error response to provide better user feedback
        let errorMessage = 'Unknown error occurred';
        try {
          const errorData = JSON.parse(errorBody);
          if (errorData.message) {
            errorMessage = errorData.message;
            if (errorData.hint) {
              errorMessage += ` Hint: ${errorData.hint}`;
            }
          }
        } catch (e) {
          errorMessage = errorBody || `HTTP ${response.status} error`;
        }

        setGenerations(prevGens =>
          prevGens.map(gen =>
            gen.id === newGeneration.id
              ? {
                  ...gen,
                  webhookResponded: true,
                  webhookError: true,
                  videoUrl: undefined,
                  errorMessage // Store the error message for display
                }
              : gen
          )
        );
      }
    })
    .catch(error => {
      console.error(`[App.tsx] Webhook fetch failed for ID ${newGeneration.id}:`, error);

      // Check if it's a timeout error
      const isTimeout = error.message && error.message.includes('timed out');
      const errorMessage = isTimeout
        ? `Video generation is taking longer than expected. This may be normal for complex videos.`
        : `Network error: ${error.message}`;

      if (isTimeout) {
        // For timeout, start polling instead of marking as failed
        console.log(`[App.tsx] Initial request timed out for ID ${newGeneration.id}, starting polling...`);
        setGenerations(prevGens =>
          prevGens.map(gen =>
            gen.id === newGeneration.id
              ? { ...gen, webhookResponded: true, pollingAttempts: 0 }
              : gen
          )
        );
        // Start polling after a short delay
        setTimeout(() => pollForVideoCompletion(newGeneration.id), WEBHOOK_CONFIG.POLLING_INTERVAL);
      } else {
        // For other errors, mark as failed
        setGenerations(prevGens =>
          prevGens.map(gen =>
            gen.id === newGeneration.id
              ? { ...gen, webhookResponded: true, webhookError: true, videoUrl: undefined, errorMessage }
              : gen
          )
        );
      }
    })
    .finally(() => {
      setIsGeneratingOverall(false);
      console.log(`[App.tsx] Webhook fetch process finalized for ID ${newGeneration.id}`);
    });
  };

  // const handleTimerComplete = (generationId: string) => { // Removed
  //   console.log(`[App.tsx] Timer complete for generation ID ${generationId}`);
  //   setGenerations(prevGens =>
  //     prevGens.map(gen => {
  //       if (gen.id === generationId) {
  //         const updatedGen = { ...gen, timerComplete: true };
  //         if (updatedGen.webhookResponded) {
  //           if (updatedGen.videoUrl && !updatedGen.webhookError) {
  //             updatedGen.status = 'completed';
  //             updatedGen.duration = updatedGen.duration ?? 420;
  //           } else { 
  //             updatedGen.status = 'failed';
  //           }
  //         }
  //         return updatedGen;
  //       }
  //       return gen;
  //     })
  //   );
  // };

  const getStatusIcon = (status: VideoGeneration['status']) => {
    switch (status) {
      case 'generating':
        return <Clock className="w-4 h-4 animate-spin text-gray-400" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getStatusText = (generation: VideoGeneration) => {
    const { status, webhookResponded, webhookError, pollingAttempts } = generation;

    if (status === 'generating') {
      if (!webhookResponded) {
        return 'Generating video, awaiting server response...';
      }
      if (webhookResponded && webhookError) {
        return 'Error processing server response.';
      }
      if (webhookResponded && pollingAttempts !== undefined && pollingAttempts > 0) {
        return `Video processing... (checking progress ${pollingAttempts}/${WEBHOOK_CONFIG.MAX_POLLING_ATTEMPTS})`;
      }
      if (webhookResponded && !webhookError) {
        return 'Video processing, checking for completion...';
      }
      return 'Processing...';
    }

    switch (status) {
      case 'completed':
        return 'Video generated successfully!';
      case 'failed':
        return generation.errorMessage || 'Generation failed. Please try again.';
      default:
        return 'Unknown status';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black text-gray-300">
      <header className="border-b border-gray-700 bg-gray-900/50 backdrop-blur-lg">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-gray-700 rounded-xl">
              <Video className="w-6 h-6 text-gray-200" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">Veo3 Video Generator</h1>
              <p className="text-sm text-gray-400">Create stunning videos with AI</p>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-6 py-8 flex flex-col h-[calc(100vh-100px)]">
        <div className="flex-1 overflow-y-auto mb-6 space-y-6">
          {generations.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center py-12">
              <div className="p-4 bg-gray-800/30 rounded-full mb-6">
                <Sparkles className="w-12 h-12 text-gray-500" />
              </div>
              <h2 className="text-2xl font-bold text-white mb-3">Welcome to Veo3 Video Generator</h2>
              <p className="text-gray-400 max-w-md">
                Describe the video you want to create and watch AI bring your vision to life. Start by typing a prompt below.
              </p>
            </div>
          ) : (
            generations.map((generation) => (
              <div
                key={generation.id}
                className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-2xl p-6 hover:bg-gray-700/60 transition-all duration-300"
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 p-2 bg-gray-700 rounded-lg">
                    <Video className="w-5 h-5 text-gray-200" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      {getStatusIcon(generation.status)}
                      <span className="text-sm font-medium text-gray-300">
                        {getStatusText(generation)}
                      </span>
                    </div>
                    <p className="text-gray-100 text-lg mb-4 leading-relaxed">
                      "{generation.prompt}"
                    </p>

                    {generation.status === 'generating' && (
                      <GenerationProgressBar
                        key={`progress-${generation.id}`}
                        isGenerating={true} // Pass true as long as status is 'generating'
                        className="h-auto"
                      />
                    )}

                    {generation.status === 'completed' && generation.videoUrl && (
                      <div className="bg-black/50 rounded-xl p-4 border border-gray-700">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm text-gray-400">Generated Video</span>
                          {generation.duration && (
                            <span className="text-xs text-gray-500">
                              {generation.duration}s
                            </span>
                          )}
                        </div>
                        <VideoPlayer
                          key={`video-${generation.id}`}
                          src={generation.videoUrl}
                          onLoad={() => console.log(`Video ${generation.id} metadata loaded`)}
                          className="h-64"
                        />
                      </div>
                    )}
                    
                    {/* The specific messages for timerComplete states are removed as timerComplete is removed. */}
                    {/* The getStatusText function now handles these messages based on webhookResponded and webhookError. */}
                    {/* We might want a specific message if webhook has responded but there's an error, handled by getStatusText */}
                    {/* Or if webhook responded but videoUrl is not yet available (still 'generating') */}

                    {generation.status === 'generating' && generation.webhookResponded && generation.webhookError && (
                       <div className="bg-yellow-800/30 rounded-xl p-4 border border-yellow-700/50 text-center mt-3">
                         <p className="text-yellow-400">Server responded with an error. Generation will be marked as failed.</p>
                       </div>
                    )}
                    
                    {generation.status === 'generating' && generation.webhookResponded && !generation.videoUrl && !generation.webhookError && (
                       <div className="bg-blue-500/10 rounded-xl p-4 border border-blue-500/20 text-center mt-3">
                         <p className="text-blue-400">Server responded. Finalizing video details...</p>
                       </div>
                    )}


                    {generation.status === 'failed' && (
                       <div className="bg-red-800/30 rounded-xl p-4 border border-red-700/50 text-center">
                         <p className="text-red-400">Video generation failed. Please try again.</p>
                       </div>
                    )}
                    
                    <div className="mt-3 text-xs text-gray-500">
                      {generation.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>

        <form onSubmit={handleSubmit} className="relative">
          <div className="bg-gray-800/70 backdrop-blur-sm border border-gray-700 rounded-2xl p-2 focus-within:border-gray-500/80 transition-all duration-300">
            <div className="flex items-end space-x-3">
              <div className="flex-1">
                <textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Describe the video you want to generate... (e.g., 'A majestic eagle soaring over snow-capped mountains at sunset')"
                  className="w-full bg-transparent text-gray-100 placeholder-gray-500 resize-none border-0 focus:ring-0 focus:outline-none p-3 text-lg min-h-[60px] max-h-32"
                  rows={2}
                  disabled={isGeneratingOverall}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmit(e);
                    }
                  }}
                />
              </div>
              <button
                type="submit"
                disabled={!prompt.trim() || isGeneratingOverall}
                className="p-3 bg-gray-600 rounded-xl text-white hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-105 active:scale-95 shadow-lg hover:shadow-gray-500/25"
              >
                {isGeneratingOverall ? (
                  <Clock className="w-5 h-5 animate-spin" />
                ) : (
                  <Send className="w-5 h-5" />
                )}
              </button>
            </div>
          </div>
        </form>
        
        <div className="flex items-center justify-between mt-3 px-2">
          <p className="text-xs text-gray-500">
            Press Enter to send, Shift+Enter for new line
          </p>
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            <span>Powered by AI</span>
            <Sparkles className="w-3 h-3" />
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;
