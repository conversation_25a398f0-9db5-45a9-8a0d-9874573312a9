import React, { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX, Maximize, Download } from 'lucide-react';

interface VideoPlayerProps {
  src: string;
  poster?: string;
  className?: string;
  onLoad?: () => void;
}

export const VideoPlayer: React.FC<VideoPlayerProps> = ({
  src,
  poster,
  className = '',
  onLoad,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showControls, setShowControls] = useState(false);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleTimeUpdate = () => {
      if (video.currentTime) setCurrentTime(video.currentTime);
    };
    const handleLoadedMetadata = () => {
      if (video.duration && !Number.isNaN(video.duration)) {
        setDuration(video.duration);
      }
      if (onLoad) onLoad();
    };

    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('loadedmetadata', handleLoadedMetadata);

    if (video.readyState >= video.HAVE_METADATA) {
      handleLoadedMetadata();
    }
    
    if (!src) {
        setDuration(0);
        setCurrentTime(0);
        setIsPlaying(false);
    }

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
    };
  }, [src, onLoad]);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
    setIsPlaying(!isPlaying);
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !isMuted;
    setIsMuted(!isMuted);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const time = parseFloat(e.target.value);
    video.currentTime = time;
    setCurrentTime(time);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = src;
    link.download = 'generated-video.mp4';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleFullscreen = () => {
    const video = videoRef.current;
    if (!video) return;

    if (video.requestFullscreen) {
      video.requestFullscreen();
    }
  };

  return (
    <div
      className={`relative group ${className}`}
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
    >
      <video
        ref={videoRef}
        src={src}
        poster={poster}
        className="w-full h-full bg-black rounded-lg object-cover"
        onClick={togglePlay}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
      />
      
      <div
        className={`absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent rounded-lg transition-opacity duration-300 ${
          showControls ? 'opacity-100' : 'opacity-0'
        }`}
      >
        <button
          onClick={togglePlay}
          className="absolute inset-0 flex items-center justify-center"
          aria-label={isPlaying ? 'Pause' : 'Play'}
        >
          <div className="p-3 bg-gray-800/60 backdrop-blur-sm rounded-full hover:bg-gray-700/80 transition-all duration-300">
            {isPlaying ? (
              <Pause className="w-8 h-8 text-gray-200" />
            ) : (
              <Play className="w-8 h-8 text-gray-200 ml-1" />
            )}
          </div>
        </button>

        <div className="absolute bottom-0 left-0 right-0 p-4">
          <div className="mb-3">
            <input
              type="range"
              min="0"
              max={duration || 0}
              value={currentTime}
              onChange={handleSeek}
              className="w-full h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
              style={{
                background: `linear-gradient(to right, #9CA3AF 0%, #9CA3AF ${ // gray-400 for fill
                  (duration > 0 ? (currentTime / duration) * 100 : 0)
                }%, #4B5563 ${ // gray-600 for track remainder
                  (duration > 0 ? (currentTime / duration) * 100 : 0)
                }%, #4B5563 100%)`,
              }}
              aria-label="Video progress"
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={togglePlay}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors duration-200"
                aria-label={isPlaying ? 'Pause' : 'Play'}
              >
                {isPlaying ? (
                  <Pause className="w-5 h-5 text-gray-300" />
                ) : (
                  <Play className="w-5 h-5 text-gray-300" />
                )}
              </button>
              
              <button
                onClick={toggleMute}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors duration-200"
                aria-label={isMuted ? 'Unmute' : 'Mute'}
              >
                {isMuted ? (
                  <VolumeX className="w-5 h-5 text-gray-300" />
                ) : (
                  <Volume2 className="w-5 h-5 text-gray-300" />
                )}
              </button>

              <span className="text-gray-300 text-sm" aria-live="polite">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={handleDownload}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors duration-200"
                title="Download video"
                aria-label="Download video"
              >
                <Download className="w-5 h-5 text-gray-300" />
              </button>
              
              <button
                onClick={handleFullscreen}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors duration-200"
                title="Fullscreen"
                aria-label="Fullscreen"
              >
                <Maximize className="w-5 h-5 text-gray-300" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
