import React, { useState, useEffect } from 'react';

interface GenerationProgressBarProps {
  // onComplete is removed as the timer no longer dictates completion
  className?: string;
  isGenerating: boolean; // New prop to control the timer
}

// const GENERATION_DURATION_SECONDS = 420; // 7 minutes - Removed

export const GenerationProgressBar: React.FC<GenerationProgressBarProps> = ({ className = '', isGenerating }) => {
  const [currentTime, setCurrentTime] = useState(0);

  useEffect(() => {
    setCurrentTime(0); // Reset on mount/remount
    let intervalId: number | undefined;

    if (isGenerating) {
      intervalId = window.setInterval(() => {
        setCurrentTime((prevTime) => prevTime + 1);
      }, 1000);
    } else {
      // If not generating (e.g., webhook received or failed), clear interval
      if (intervalId) {
        window.clearInterval(intervalId);
      }
      // Optionally, you might want to keep the final time displayed
      // or reset currentTime if isGenerating becomes false and it was previously true.
      // For now, it just stops counting.
    }

    return () => {
      if (intervalId) {
        window.clearInterval(intervalId);
      }
    };
  }, [isGenerating]); // Rerun effect if isGenerating changes

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // const progressPercentage = (currentTime / GENERATION_DURATION_SECONDS) * 100; // No longer a fixed duration

  return (
    <div className={`w-full bg-gray-800/50 rounded-xl p-4 border border-gray-700 ${className}`}> {/* Updated background and border */}
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm text-gray-300">Generating Video...</span>
        <span className="text-sm text-gray-200"> {/* Slightly dimmer time text */}
          {formatTime(currentTime)}
        </span>
      </div>
      <div className="w-full h-2 bg-gray-700 rounded-lg overflow-hidden"> {/* Progress bar track */}
        {/* Indeterminate progress bar or simply a pulsing animation could go here */}
        {/* For now, let's make it a simple bar that doesn't show percentage */}
        <div
          className="h-full bg-gray-500 rounded-lg animate-pulse" // Simple pulsing animation
          style={{ width: `100%` }} // Or a specific small width if you prefer a "throbber"
        />
      </div>
    </div>
  );
};
