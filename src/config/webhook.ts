// Webhook configuration
export const WEBHOOK_CONFIG = {
  // Use proxy in development to avoid CORS issues
  getWebhookUrl: () => {
    const useProxy = import.meta.env.VITE_USE_PROXY === 'true';
    const baseUrl = import.meta.env.VITE_WEBHOOK_BASE_URL || 'https://topical-weekly-firefly.ngrok-free.app';
    const path = import.meta.env.VITE_WEBHOOK_PATH || '/webhook-test/81d49a65-3482-48d3-87ab-8cab0477cea1';
    
    if (useProxy || import.meta.env.DEV) {
      // Use proxy endpoint in development
      return `/api${path}`;
    } else {
      // Use direct URL in production
      return `${baseUrl}${path}`;
    }
  },
  
  getHeaders: () => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    // Only add ngrok header when making direct requests (not through proxy)
    if (!import.meta.env.DEV && !import.meta.env.VITE_USE_PROXY) {
      headers['ngrok-skip-browser-warning'] = 'true';
    }
    
    return headers;
  }
};
