// Webhook configuration
export const WEBHOOK_CONFIG = {
  // Timeout and polling configuration
  INITIAL_TIMEOUT: 8 * 60 * 1000, // 8 minutes for initial request
  POLLING_INTERVAL: 30 * 1000, // Poll every 30 seconds
  MAX_POLLING_ATTEMPTS: 16, // Maximum 16 polling attempts (8 minutes total)

  // Use proxy in development to avoid CORS issues
  getWebhookUrl: () => {
    const useProxy = import.meta.env.VITE_USE_PROXY === 'true';
    const baseUrl = import.meta.env.VITE_WEBHOOK_BASE_URL || 'https://topical-weekly-firefly.ngrok-free.app';
    const path = import.meta.env.VITE_WEBHOOK_PATH || '/webhook/81d49a65-3482-48d3-87ab-8cab0477cea1';

    if (useProxy || import.meta.env.DEV) {
      // Use proxy endpoint in development
      return `/api${path}`;
    } else {
      // Use direct URL in production
      return `${baseUrl}${path}`;
    }
  },

  getHeaders: () => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Only add ngrok header when making direct requests (not through proxy)
    if (!import.meta.env.DEV && !import.meta.env.VITE_USE_PROXY) {
      headers['ngrok-skip-browser-warning'] = 'true';
    }

    return headers;
  },

  // Create fetch with timeout
  fetchWithTimeout: async (url: string, options: RequestInit, timeoutMs: number) => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`Request timed out after ${timeoutMs / 1000} seconds`);
      }
      throw error;
    }
  }
};
