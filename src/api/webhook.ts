// Webhook handler for video generation
export interface VideoGenerationRequest {
  prompt: string;
  id: string;
  userId?: string;
  options?: {
    duration?: number;
    quality?: 'standard' | 'high' | 'premium';
    aspectRatio?: '16:9' | '9:16' | '1:1';
  };
}

export interface VideoGenerationResponse {
  success: boolean;
  videoUrl?: string;
  duration?: number;
  error?: string;
  jobId?: string;
}

export class VideoGenerationWebhook {
  private static readonly WEBHOOK_URL = process.env.VITE_VIDEO_GENERATION_WEBHOOK_URL || '/api/generate-video';
  
  static async generateVideo(request: VideoGenerationRequest): Promise<VideoGenerationResponse> {
    try {
      const response = await fetch(this.WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.VITE_API_KEY || ''}`,
        },
        body: JSON.stringify({
          ...request,
          timestamp: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: VideoGenerationResponse = await response.json();
      return result;
    } catch (error) {
      console.error('Video generation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  static async checkStatus(jobId: string): Promise<VideoGenerationResponse> {
    try {
      const response = await fetch(`${this.WEBHOOK_URL}/status/${jobId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.VITE_API_KEY || ''}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: VideoGenerationResponse = await response.json();
      return result;
    } catch (error) {
      console.error('Status check failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Status check failed',
      };
    }
  }
}

// Example webhook payload structure for your backend
export const exampleWebhookPayload = {
  prompt: "A majestic eagle soaring over snow-capped mountains at sunset",
  id: "gen_1234567890",
  userId: "user_abc123",
  timestamp: "2024-01-15T10:30:00.000Z",
  options: {
    duration: 10,
    quality: "high",
    aspectRatio: "16:9",
  }
};

// Example response structure your webhook should return
export const exampleWebhookResponse = {
  success: true,
  jobId: "job_9876543210",
  estimatedDuration: 180, // seconds until completion
  videoUrl: null, // will be populated when complete
};