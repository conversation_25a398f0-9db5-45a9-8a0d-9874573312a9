import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  server: {
    proxy: {
      '/api': {
        target: 'https://topical-weekly-firefly.ngrok-free.app',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // Add ngrok header to bypass browser warning
            proxyReq.setHeader('ngrok-skip-browser-warning', 'true');
            console.log(`[Vite Proxy] Proxying request: ${req.method} ${req.url} -> ${proxyReq.getHeader('host')}${proxyReq.path}`);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log(`[Vite Proxy] Response: ${proxyRes.statusCode} for ${req.method} ${req.url}`);
          });
          proxy.on('error', (err, req, res) => {
            console.error(`[Vite Proxy] Error for ${req.method} ${req.url}:`, err);
          });
        },
      },
    },
  },
});
